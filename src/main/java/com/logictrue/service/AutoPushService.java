package com.logictrue.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.logictrue.config.ConfigManager;
import com.logictrue.iot.entity.DeviceDetectionData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URI;
import java.net.URLEncoder;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 自动推送服务类
 * 负责在心跳成功后自动推送待推送的数据记录
 */
public class AutoPushService {
    private static final Logger logger = LoggerFactory.getLogger(AutoPushService.class);

    private final DeviceDetectionDataService deviceDetectionDataService;
    private final ConfigManager configManager;
    private final HttpClient httpClient;

    public AutoPushService() {
        this.deviceDetectionDataService = new DeviceDetectionDataService();
        this.configManager = ConfigManager.getInstance();
        this.httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(30))
                .build();
    }

    /**
     * 执行自动推送任务
     * 在心跳成功后调用此方法
     */
    public CompletableFuture<PushResult> performAutoPush() {
        return CompletableFuture.supplyAsync(() -> {
            try {
                logger.info("开始执行自动推送任务");

                // 检查是否启用自动推送
                if (!configManager.isAutoPushEnabled()) {
                    logger.info("自动推送功能已禁用，跳过推送");
                    return PushResult.success(0, "自动推送功能已禁用");
                }

                // 查询待推送记录
                List<DeviceDetectionData> allPendingRecords = deviceDetectionDataService.getPendingPushRecords();
                if (allPendingRecords.isEmpty()) {
                    logger.info("没有待推送的记录");
                    return PushResult.success(0, "没有待推送的记录");
                }

                // 过滤掉已经超过重试次数限制的记录
                List<DeviceDetectionData> pendingRecords = allPendingRecords.stream()
                        .filter(record -> !isRetryLimitExceeded(record))
                        .collect(Collectors.toList());

                int filteredCount = allPendingRecords.size() - pendingRecords.size();
                if (filteredCount > 0) {
                    logger.info("过滤掉{}条已超过重试次数限制的记录", filteredCount);
                }

                if (pendingRecords.isEmpty()) {
                    logger.info("没有可推送的记录（所有记录都已超过重试次数限制）");
                    return PushResult.success(0, "没有可推送的记录");
                }

                logger.info("找到{}条待推送记录（总共{}条，过滤{}条）",
                        pendingRecords.size(), allPendingRecords.size(), filteredCount);

                // 循环推送每条记录
                int successCount = 0;
                int failureCount = 0;
                String pushUrl = configManager.getPushUrl();

                for (DeviceDetectionData record : pendingRecords) {
                    try {
                        // 检查重试次数是否已超过限制
                        if (isRetryLimitExceeded(record)) {
                            logger.warn("推送重试次数已超过限制，跳过推送，ID: {}, 当前重试次数: {}",
                                    record.getId(), record.getPushRetryCount());
                            failureCount++;
                            continue;
                        }

                        boolean pushSuccess = pushSingleRecord(record, pushUrl);
                        if (pushSuccess) {
                            successCount++;
                        } else {
                            failureCount++;
                        }
                    } catch (Exception e) {
                        logger.error("推送记录失败，ID: {}", record.getId(), e);
                        failureCount++;
                        // 更新推送失败状态
                        updatePushFailureStatus(record, pushUrl, e.getMessage());
                    }
                }

                String message = String.format("推送完成，成功: %d, 失败: %d", successCount, failureCount);
                logger.info(message);

                return PushResult.success(successCount, message);

            } catch (Exception e) {
                logger.error("执行自动推送任务异常", e);
                return PushResult.failure("执行自动推送任务异常: " + e.getMessage());
            }
        });
    }

    /**
     * 推送单条记录
     */
    private boolean pushSingleRecord(DeviceDetectionData record, String pushUrl) {
        try {
            logger.info("开始推送记录，ID: {}, 设备编码: {}", record.getId(), record.getDeviceCode());

            // 检查是否有文件需要推送
            String filePath = getFilePathForPush(record);
            if (filePath == null) {
                logger.warn("未找到要推送的文件，ID: {}", record.getId());
                updatePushFailureStatus(record, pushUrl, "未找到要推送的文件");
                return false;
            }

            // 构建multipart表单数据
            String boundary = "----WebKitFormBoundary" + System.currentTimeMillis();
            String multipartData = buildMultipartFormData(record, filePath, boundary);
            if (multipartData == null) {
                logger.error("构建multipart表单数据失败，ID: {}", record.getId());
                updatePushFailureStatus(record, pushUrl, "构建表单数据失败");
                return false;
            }

            // 发送HTTP multipart表单请求
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(pushUrl))
                    .timeout(Duration.ofSeconds(configManager.getPushTimeoutSeconds()))
                    .header("Content-Type", "multipart/form-data; boundary=" + boundary)
                    .POST(HttpRequest.BodyPublishers.ofString(multipartData))
                    .build();

            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

            // 检查响应状态
            if (response.statusCode() >= 200 && response.statusCode() < 300) {
                // HTTP状态码正常，进一步检查响应JSON中的code字段
                String responseBody = response.body();

                PushCheckResult businessResult = checkBusinessSuccess(responseBody);
                if (businessResult.isSuccess()) {
                    logger.info("推送成功，ID: {}, 状态码: {}", record.getId(), response.statusCode());
                    // 更新推送成功状态
                    deviceDetectionDataService.updatePushSuccess(record.getId(), pushUrl);
                    return true;
                } else {
                    logger.warn("推送业务失败，ID: {}, 错误信息: {}", record.getId(), businessResult.getErrorMessage());
                    // 使用业务层返回的具体错误信息
                    updatePushFailureStatus(record, pushUrl, businessResult.getErrorMessage());
                    return false;
                }
            } else {
                String errorMessage = String.format("推送HTTP失败，状态码: %d, 响应: %s",
                    response.statusCode(), response.body());
                logger.warn("推送HTTP失败，ID: {}, {}", record.getId(), errorMessage);
                // 更新推送失败状态
                updatePushFailureStatus(record, pushUrl, errorMessage);
                return false;
            }

        } catch (Exception e) {
            logger.error("推送记录异常，ID: {}", record.getId(), e);
            updatePushFailureStatus(record, pushUrl, e.getMessage());
            return false;
        }
    }

    /**
     * 获取要推送的文件路径
     */
    private String getFilePathForPush(DeviceDetectionData record) {
        // 优先使用collectPath
        if (record.getCollectPath() != null && !record.getCollectPath().trim().isEmpty()) {
            String collectPath = record.getCollectPath().trim();
            Path path = Paths.get(collectPath);
            if (Files.exists(path) && Files.isRegularFile(path)) {
                return collectPath;
            }
        }

        // 其次使用filePath
        if (record.getFilePath() != null && !record.getFilePath().trim().isEmpty()) {
            String filePath = record.getFilePath().trim();
            Path path = Paths.get(filePath);
            if (Files.exists(path) && Files.isRegularFile(path)) {
                return filePath;
            }
        }

        return null;
    }

    /**
     * 构建multipart表单数据
     */
    private String buildMultipartFormData(DeviceDetectionData record, String filePath, String boundary) {
        try {
            StringBuilder formData = new StringBuilder();
            String lineEnd = "\r\n";
            String twoHyphens = "--";

            // 添加设备编码
            if (record.getDeviceCode() != null) {
                formData.append(twoHyphens).append(boundary).append(lineEnd);
                formData.append("Content-Disposition: form-data; name=\"deviceCode\"").append(lineEnd);
                formData.append(lineEnd);
                formData.append(record.getDeviceCode()).append(lineEnd);
            }

            // 添加模板ID（如果有的话，否则使用默认值）
            Long templateId = record.getTemplateId();
            if (templateId == null) {
                templateId = 1L; // 默认模板ID
            }
            formData.append(twoHyphens).append(boundary).append(lineEnd);
            formData.append("Content-Disposition: form-data; name=\"templateId\"").append(lineEnd);
            formData.append(lineEnd);
            formData.append(templateId).append(lineEnd);

            // 添加collectData字段的JSON数据（进行URL编码）
            String collectData = "{}";
            if (record.getCollectData() != null && !record.getCollectData().trim().isEmpty()) {
                collectData = record.getCollectData().trim();
            }
            // URL编码collectData
            String encodedCollectData = URLEncoder.encode(collectData, StandardCharsets.UTF_8);
            formData.append(twoHyphens).append(boundary).append(lineEnd);
            formData.append("Content-Disposition: form-data; name=\"collectData\"").append(lineEnd);
            formData.append(lineEnd);
            formData.append(encodedCollectData).append(lineEnd);

            // 添加文件
            if (filePath != null) {
                try {
                    byte[] fileBytes = Files.readAllBytes(Paths.get(filePath));
                    String fileName = Paths.get(filePath).getFileName().toString();

                    formData.append(twoHyphens).append(boundary).append(lineEnd);
                    formData.append("Content-Disposition: form-data; name=\"file\"; filename=\"").append(fileName).append("\"").append(lineEnd);
                    formData.append("Content-Type: application/octet-stream").append(lineEnd);
                    formData.append(lineEnd);

                    // 将文件内容转换为Base64字符串添加到表单中
                    String fileBase64 = java.util.Base64.getEncoder().encodeToString(fileBytes);
                    formData.append(fileBase64).append(lineEnd);

                } catch (IOException e) {
                    logger.error("读取文件失败，路径: {}", filePath, e);
                    return null;
                }
            }

            // 结束边界
            formData.append(twoHyphens).append(boundary).append(twoHyphens).append(lineEnd);

            return formData.toString();

        } catch (Exception e) {
            logger.error("构建multipart表单数据失败，ID: {}", record.getId(), e);
            return null;
        }
    }



    /**
     * 检查业务层面是否成功
     * 解析响应JSON，检查code字段，返回详细的结果信息
     */
    private PushCheckResult checkBusinessSuccess(String responseBody) {
        try {
            if (responseBody == null || responseBody.trim().isEmpty()) {
                logger.warn("响应体为空，视为推送失败");
                return PushCheckResult.failure("响应体为空");
            }

            // 解析JSON响应
            JSONObject jsonResponse = JSON.parseObject(responseBody);

            // 检查code字段
            Integer code = jsonResponse.getInteger("code");
            if (code == null) {
                logger.warn("响应JSON中没有code字段，视为推送失败，响应: {}", responseBody);
                return PushCheckResult.failure("响应JSON中没有code字段");
            }

            // code为200表示成功，其他值（包括500）表示失败
            if (code == 200) {
                // 进一步检查isSuccess字段（如果存在）
                Boolean isSuccess = jsonResponse.getBoolean("isSuccess");
                if (isSuccess != null && !isSuccess) {
                    String msg = jsonResponse.getString("msg");
                    String errorMsg = "isSuccess为false" + (msg != null ? ": " + msg : "");
                    logger.warn("响应JSON中isSuccess为false，视为推送失败，响应: {}", responseBody);
                    return PushCheckResult.failure(errorMsg);
                }

                // 检查isError字段（如果存在）
                Boolean isError = jsonResponse.getBoolean("isError");
                if (isError != null && isError) {
                    String msg = jsonResponse.getString("msg");
                    String errorMsg = "isError为true" + (msg != null ? ": " + msg : "");
                    logger.warn("响应JSON中isError为true，视为推送失败，响应: {}", responseBody);
                    return PushCheckResult.failure(errorMsg);
                }

                return PushCheckResult.success("推送成功");
            } else {
                // code不为200，使用msg作为错误信息
                String msg = jsonResponse.getString("msg");
                String errorMsg = msg != null && !msg.trim().isEmpty() ? msg : "业务处理失败";
                logger.warn("推送业务失败，code: {}, msg: {}", code, msg);
                return PushCheckResult.failure(errorMsg);
            }

        } catch (Exception e) {
            logger.error("解析响应JSON失败，视为推送失败，响应: {}", responseBody, e);
            return PushCheckResult.failure("解析响应JSON失败: " + e.getMessage());
        }
    }

    /**
     * 检查重试次数是否已超过限制
     */
    private boolean isRetryLimitExceeded(DeviceDetectionData record) {
        Integer currentRetryCount = record.getPushRetryCount() != null ? record.getPushRetryCount() : 0;
        int maxRetryTimes = configManager.getPushRetryTimes();

        return currentRetryCount >= maxRetryTimes;
    }

    /**
     * 更新推送失败状态
     */
    private void updatePushFailureStatus(DeviceDetectionData record, String pushUrl, String errorMessage) {
        try {
            // 获取当前重试次数，如果为null则设为0
            Integer currentRetryCount = record.getPushRetryCount() != null ? record.getPushRetryCount() : 0;
            Integer newRetryCount = currentRetryCount + 1;
            int maxRetryTimes = configManager.getPushRetryTimes();

            // 检查是否超过最大重试次数
            if (newRetryCount >= maxRetryTimes) {
                logger.warn("推送重试次数已达到上限，设置为最终失败状态，ID: {}, 重试次数: {}/{}",
                        record.getId(), newRetryCount, maxRetryTimes);

                String finalErrorMessage = String.format("推送失败，已重试%d次达到上限。错误信息: %s",
                        newRetryCount, errorMessage);
                deviceDetectionDataService.updatePushFailure(record.getId(), pushUrl, finalErrorMessage, newRetryCount);
            } else {
                logger.info("推送失败，将进行重试，ID: {}, 当前重试次数: {}/{}",
                        record.getId(), newRetryCount, maxRetryTimes);
                deviceDetectionDataService.updatePushFailure(record.getId(), pushUrl, errorMessage, newRetryCount);
            }

        } catch (Exception e) {
            logger.error("更新推送失败状态异常，ID: {}", record.getId(), e);
        }
    }

    /**
     * 重新推送指定记录（手动推送）
     */
    public CompletableFuture<Boolean> retryPushRecord(Long recordId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                logger.info("开始手动重新推送记录，ID: {}", recordId);

                // 查询记录
                DeviceDetectionData record = deviceDetectionDataService.getById(recordId);
                if (record == null) {
                    logger.warn("未找到要重新推送的记录，ID: {}", recordId);
                    return false;
                }

                // 手动推送时重置推送状态和重试次数
                logger.info("手动推送，重置推送状态和重试次数，ID: {}", recordId);
                deviceDetectionDataService.resetPushStatus(recordId);

                // 重新查询记录以获取最新状态
                record = deviceDetectionDataService.getById(recordId);
                if (record == null) {
                    logger.error("重置状态后无法查询到记录，ID: {}", recordId);
                    return false;
                }

                // 执行推送
                String pushUrl = configManager.getPushUrl();
                boolean pushSuccess = pushSingleRecord(record, pushUrl);

                if (pushSuccess) {
                    logger.info("手动推送成功，ID: {}", recordId);
                } else {
                    logger.warn("手动推送失败，ID: {}", recordId);
                }

                return pushSuccess;

            } catch (Exception e) {
                logger.error("手动重新推送记录异常，ID: {}", recordId, e);
                return false;
            }
        });
    }

    /**
     * 内部推送检查结果类
     */
    private static class PushCheckResult {
        private final boolean success;
        private final String errorMessage;

        private PushCheckResult(boolean success, String errorMessage) {
            this.success = success;
            this.errorMessage = errorMessage;
        }

        public static PushCheckResult success(String message) {
            return new PushCheckResult(true, message);
        }

        public static PushCheckResult failure(String errorMessage) {
            return new PushCheckResult(false, errorMessage);
        }

        public boolean isSuccess() {
            return success;
        }

        public String getErrorMessage() {
            return errorMessage;
        }
    }

    /**
     * 推送结果数据类
     */
    public static class PushResult {
        private final boolean success;
        private final int successCount;
        private final String message;

        private PushResult(boolean success, int successCount, String message) {
            this.success = success;
            this.successCount = successCount;
            this.message = message;
        }

        public static PushResult success(int successCount, String message) {
            return new PushResult(true, successCount, message);
        }

        public static PushResult failure(String message) {
            return new PushResult(false, 0, message);
        }

        public boolean isSuccess() {
            return success;
        }

        public int getSuccessCount() {
            return successCount;
        }

        public String getMessage() {
            return message;
        }
    }
}
