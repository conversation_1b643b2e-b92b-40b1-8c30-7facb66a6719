package com.logictrue.service;

import com.alibaba.fastjson2.JSON;
import com.logictrue.config.ConfigManager;
import com.logictrue.iot.entity.DeviceDetectionData;
import com.logictrue.model.FormField;
import javafx.scene.control.Control;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 数据采集服务类
 * 负责执行数据采集任务，包括动态表单验证和数据保存
 */
public class DataCollectionService {
    private static final Logger logger = LoggerFactory.getLogger(DataCollectionService.class);

    private final ConfigManager configManager;
    private final FormValidationService formValidationService;
    private final ExcelParsingService excelParsingService;
    private final TemplateService templateService;
    private final DeviceDetectionDataService deviceDetectionDataService;

    public DataCollectionService() {
        this.configManager = ConfigManager.getInstance();
        this.formValidationService = new FormValidationService();
        this.excelParsingService = new ExcelParsingService();
        this.templateService = new TemplateService();
        this.deviceDetectionDataService = new DeviceDetectionDataService();
    }

    /**
     * 执行数据采集任务
     *
     * @param fieldControls 动态表单控件映射
     * @return 数据采集结果
     */
    public CompletableFuture<DataCollectionResult> performDataCollection(Map<String, Control> fieldControls) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                logger.info("开始执行数据采集任务");

                // 1. 验证配置
                DataCollectionResult configValidation = validateConfiguration();
                if (!configValidation.isSuccess()) {
                    return configValidation;
                }

                // 2. 收集并验证动态表单数据
                Map<String, Object> formData = collectFormData(fieldControls);
                FormValidationService.ValidationResult validationResult = formValidationService.validateRequiredFields(formData);

                if (!validationResult.isValid()) {
                    return DataCollectionResult.failure("表单验证失败: " + buildValidationErrorMessage(validationResult));
                }

                // 3. 将表单数据转换为JSON字符串
                String collectDataJson = JSON.toJSONString(formData);
                logger.info("动态表单数据已转换为JSON: {}", collectDataJson);

                // 4. 执行Excel数据采集
                String deviceId = configManager.getConfig().getDeviceId();
                String excelCollectionPath = configManager.getExcelCollectionPath();
                String templateDownloadUrl = configManager.getTemplateDownloadUrl();

                DataCollectionResult collectionResult = performExcelDataCollection(
                    deviceId, excelCollectionPath, templateDownloadUrl, collectDataJson);

                if (collectionResult.isSuccess()) {
                    logger.info("数据采集任务完成，成功处理{}个文件", collectionResult.getProcessedCount());
                } else {
                    logger.warn("数据采集任务失败: {}", collectionResult.getErrorMessage());
                }

                return collectionResult;

            } catch (Exception e) {
                logger.error("数据采集任务异常", e);
                return DataCollectionResult.failure("数据采集异常: " + e.getMessage());
            }
        });
    }

    /**
     * 验证配置信息
     */
    private DataCollectionResult validateConfiguration() {
        String excelCollectionPath = configManager.getExcelCollectionPath();
        if (excelCollectionPath == null || excelCollectionPath.trim().isEmpty()) {
            return DataCollectionResult.failure("请先在设置中配置Excel文件采集路径");
        }

        String templateDownloadUrl = configManager.getTemplateDownloadUrl();
        if (templateDownloadUrl == null || templateDownloadUrl.trim().isEmpty()) {
            return DataCollectionResult.failure("请先在设置中配置模板下载地址");
        }

        return DataCollectionResult.success();
    }

    /**
     * 收集动态表单数据
     */
    private Map<String, Object> collectFormData(Map<String, Control> fieldControls) {
        Map<String, Object> formData = new HashMap<>();

        for (Map.Entry<String, Control> entry : fieldControls.entrySet()) {
            String fieldName = entry.getKey();
            Control control = entry.getValue();
            Object value = getControlValue(control);

            if (value != null) {
                formData.put(fieldName, value);
            }

            logger.debug("收集表单数据: {} = {}", fieldName, value);
        }

        // 添加系统字段
        formData.put("deviceId", configManager.getConfig().getDeviceId());
        formData.put("collectTime", LocalDateTime.now().toString());

        logger.info("收集表单数据完成，共{}个字段", formData.size());
        return formData;
    }

    /**
     * 获取控件的值
     */
    private Object getControlValue(Control control) {
        if (control instanceof javafx.scene.control.TextField) {
            return ((javafx.scene.control.TextField) control).getText();
        } else if (control instanceof javafx.scene.control.TextArea) {
            return ((javafx.scene.control.TextArea) control).getText();
        } else if (control instanceof javafx.scene.control.DatePicker) {
            return ((javafx.scene.control.DatePicker) control).getValue();
        } else if (control instanceof javafx.scene.control.ComboBox) {
            return ((javafx.scene.control.ComboBox<?>) control).getValue();
        } else if (control instanceof javafx.scene.control.CheckBox) {
            return ((javafx.scene.control.CheckBox) control).isSelected();
        }
        return null;
    }

    /**
     * 构建验证错误消息
     */
    private String buildValidationErrorMessage(FormValidationService.ValidationResult validationResult) {
        StringBuilder message = new StringBuilder();

        if (!validationResult.getMissingFields().isEmpty()) {
            message.append("缺少必填字段: ").append(String.join(", ", validationResult.getMissingFields()));
        }

        if (!validationResult.getEmptyFields().isEmpty()) {
            if (message.length() > 0) {
                message.append("; ");
            }
            message.append("必填字段为空: ").append(String.join(", ", validationResult.getEmptyFields()));
        }

        return message.toString();
    }

    /**
     * 执行Excel数据采集的具体逻辑
     */
    private DataCollectionResult performExcelDataCollection(String deviceId, String excelCollectionPath,
                                                          String templateDownloadUrl, String collectDataJson) {
        try {
            logger.info("开始执行Excel数据采集，路径: {}", excelCollectionPath);

            // 1. 扫描Excel文件
            List<File> excelFiles = scanExcelFiles(excelCollectionPath);
            if (excelFiles.isEmpty()) {
                logger.warn("未找到Excel文件，路径: {}", excelCollectionPath);
                return DataCollectionResult.success(0); // 没有文件不算失败
            }

            logger.info("找到{}个Excel文件待处理", excelFiles.size());

            // 2. 获取或下载模板文件
            String templateFilePath = getOrDownloadTemplate(templateDownloadUrl);
            if (templateFilePath == null) {
                return DataCollectionResult.failure("无法获取模板文件");
            }

            // 3. 处理每个Excel文件
            int successCount = 0;
            int totalCount = excelFiles.size();

            for (File excelFile : excelFiles) {
                try {
                    logger.info("开始处理Excel文件: {}", excelFile.getName());

                    // 解析Excel文件（ExcelParsingService会自己创建和管理DeviceDetectionData记录）
                    ExcelParsingService.ExcelParsingResult parsingResult =
                            excelParsingService.parseExcelFile(deviceId, excelFile.getAbsolutePath(), templateFilePath);

                    if (parsingResult.isSuccess()) {
                        // 解析成功后的处理流程
                        boolean processSuccess = processAfterParsingSuccess(deviceId, excelFile, collectDataJson);
                        if (processSuccess) {
                            successCount++;
                            logger.info("Excel文件处理完成: {}", excelFile.getName());
                        } else {
                            logger.warn("Excel文件解析成功，但后续处理失败: {}", excelFile.getName());
                        }
                    } else {
                        // 解析失败后的处理
                        processAfterParsingFailure(deviceId, excelFile.getAbsolutePath(), parsingResult.getErrorMessage());
                        logger.error("解析Excel文件失败: {}, 错误: {}", excelFile.getName(), parsingResult.getErrorMessage());
                    }

                } catch (Exception e) {
                    logger.error("处理Excel文件异常: {}", excelFile.getName(), e);
                }
            }

            logger.info("Excel数据采集完成，总文件数: {}, 成功处理: {}", totalCount, successCount);
            return DataCollectionResult.success(successCount);

        } catch (Exception e) {
            logger.error("Excel数据采集异常", e);
            return DataCollectionResult.failure("Excel数据采集异常: " + e.getMessage());
        }
    }

    /**
     * 更新DeviceDetectionData记录，添加动态表单数据
     */
    private boolean updateDetectionDataWithCollectData(String deviceId, String filePath, String collectDataJson) {
        try {
            // 根据设备ID和文件路径查找最新的记录
            DeviceDetectionData detectionData = findLatestDetectionDataByDeviceAndFile(deviceId, filePath);
            if (detectionData == null) {
                logger.error("未找到对应的DeviceDetectionData记录，设备ID: {}, 文件: {}", deviceId, filePath);
                return false;
            }

            // 更新collectData字段
            boolean updateSuccess = updateCollectDataField(detectionData.getId(), collectDataJson);
            if (updateSuccess) {
                logger.info("成功更新DeviceDetectionData记录的collectData字段，ID: {}, 数据长度: {}",
                           detectionData.getId(), collectDataJson.length());
                return true;
            } else {
                logger.error("更新DeviceDetectionData记录的collectData字段失败，ID: {}", detectionData.getId());
                return false;
            }

        } catch (Exception e) {
            logger.error("更新DeviceDetectionData记录异常，设备ID: {}, 文件: {}", deviceId, filePath, e);
            return false;
        }
    }

    /**
     * 根据设备ID和文件路径查找最新的DeviceDetectionData记录
     */
    private DeviceDetectionData findLatestDetectionDataByDeviceAndFile(String deviceId, String filePath) {
        try {
            // 这里需要调用数据库服务来查找记录
            // 由于当前的DeviceDetectionDataService没有提供按设备ID和文件路径查找的方法
            // 我们需要添加这个功能或者使用其他方式
            return deviceDetectionDataService.findLatestByDeviceAndFile(deviceId, filePath);
        } catch (Exception e) {
            logger.error("查找DeviceDetectionData记录失败，设备ID: {}, 文件: {}", deviceId, filePath, e);
            return null;
        }
    }

    /**
     * 更新DeviceDetectionData记录的collectData字段
     */
    private boolean updateCollectDataField(Long detectionDataId, String collectDataJson) {
        try {
            return deviceDetectionDataService.updateCollectData(detectionDataId, collectDataJson);
        } catch (Exception e) {
            logger.error("更新collectData字段失败，ID: {}", detectionDataId, e);
            return false;
        }
    }

    /**
     * 解析成功后的处理流程
     */
    private boolean processAfterParsingSuccess(String deviceId, File excelFile, String collectDataJson) {
        try {
            String originalFilePath = excelFile.getAbsolutePath();

            // 1. 查找对应的数据库记录
            DeviceDetectionData detectionData = findLatestDetectionDataByDeviceAndFile(deviceId, originalFilePath);
            if (detectionData == null) {
                logger.error("未找到对应的DeviceDetectionData记录，设备ID: {}, 文件: {}", deviceId, originalFilePath);
                return false;
            }

            // 2. 移动文件到目标目录
            String targetFilePath = moveFileToDataDirectory(excelFile);
            if (targetFilePath == null) {
                logger.error("移动文件失败: {}", originalFilePath);
                return false;
            }

            // 3. 更新数据库记录
            boolean updateSuccess = updateAfterParsingSuccess(detectionData.getId(), collectDataJson, targetFilePath);
            if (updateSuccess) {
                logger.info("解析成功后处理完成，文件已移动到: {}", targetFilePath);
                return true;
            } else {
                logger.error("更新数据库记录失败，ID: {}", detectionData.getId());
                return false;
            }

        } catch (Exception e) {
            logger.error("解析成功后处理异常，设备ID: {}, 文件: {}", deviceId, excelFile.getName(), e);
            return false;
        }
    }

    /**
     * 解析失败后的处理流程
     */
    private void processAfterParsingFailure(String deviceId, String filePath, String errorMessage) {
        try {
            // 查找对应的数据库记录并更新状态
            DeviceDetectionData detectionData = findLatestDetectionDataByDeviceAndFile(deviceId, filePath);
            if (detectionData != null) {
                deviceDetectionDataService.updateAfterParsing(
                    detectionData.getId(),
                    2, // 解析失败状态
                    "解析失败: " + errorMessage,
                    null, null, null, null, "system"
                );
                logger.info("已更新解析失败状态，记录ID: {}", detectionData.getId());
            }
        } catch (Exception e) {
            logger.error("处理解析失败状态异常，设备ID: {}, 文件: {}", deviceId, filePath, e);
        }
    }

    /**
     * 获取或下载模板文件
     */
    private String getOrDownloadTemplate(String templateDownloadUrl) {
        try {
            if (templateService.hasLocalTemplateFiles()) {
                String templateFilePath = templateService.getLatestLocalTemplateFile();
                logger.info("使用本地模板文件: {}", templateFilePath);
                return templateFilePath;
            } else {
                logger.info("本地未找到模板文件，尝试从配置的地址下载模板");
                String templateFilePath = templateService.downloadTemplateFromUrl(templateDownloadUrl).get();

                if (templateFilePath != null) {
                    logger.info("模板下载成功: {}", templateFilePath);
                    return templateFilePath;
                } else {
                    logger.error("模板下载失败");
                    return null;
                }
            }
        } catch (Exception e) {
            logger.error("获取模板文件失败", e);
            return null;
        }
    }

    /**
     * 扫描指定路径下的Excel文件（包括子目录）
     */
    private List<File> scanExcelFiles(String dirPath) {
        List<File> excelFiles = new ArrayList<>();

        try {
            File dir = new File(dirPath);
            if (!dir.exists() || !dir.isDirectory()) {
                logger.warn("Excel采集路径不存在或不是目录: {}", dirPath);
                return excelFiles;
            }

            // 递归扫描所有子目录
            scanExcelFilesRecursively(dir, excelFiles);

            logger.info("扫描Excel文件完成，共找到{}个文件", excelFiles.size());
            if (logger.isDebugEnabled()) {
                for (File file : excelFiles) {
                    logger.debug("找到Excel文件: {}", file.getAbsolutePath());
                }
            }

        } catch (Exception e) {
            logger.error("扫描Excel文件失败: {}", dirPath, e);
        }

        return excelFiles;
    }

    /**
     * 递归扫描目录下的Excel文件
     */
    private void scanExcelFilesRecursively(File directory, List<File> excelFiles) {
        try {
            File[] files = directory.listFiles();
            if (files == null) {
                logger.warn("无法读取目录内容: {}", directory.getAbsolutePath());
                return;
            }

            for (File file : files) {
                if (file.isFile()) {
                    // 检查是否为Excel文件
                    if (isExcelFile(file)) {
                        excelFiles.add(file);
                        logger.debug("添加Excel文件: {}", file.getAbsolutePath());
                    }
                } else if (file.isDirectory()) {
                    // 递归扫描子目录
                    logger.debug("扫描子目录: {}", file.getAbsolutePath());
                    scanExcelFilesRecursively(file, excelFiles);
                }
            }

        } catch (Exception e) {
            logger.error("递归扫描目录失败: {}", directory.getAbsolutePath(), e);
        }
    }

    /**
     * 判断文件是否为Excel文件
     */
    private boolean isExcelFile(File file) {
        if (file == null || !file.isFile()) {
            return false;
        }

        String fileName = file.getName().toLowerCase();
        return fileName.endsWith(".xlsx") || fileName.endsWith(".xls");
    }

    /**
     * 移动文件到data目录下的当前日期目录
     */
    private String moveFileToDataDirectory(File sourceFile) {
        try {
            // 获取jar文件所在目录
            String jarDir = getJarDirectory();

            // 创建目标目录：jar同级目录/data/yyyy-MM-dd/
            String currentDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            Path targetDir = Paths.get(jarDir, "data", currentDate);

            // 确保目标目录存在
            Files.createDirectories(targetDir);

            // 构建目标文件路径
            Path targetFilePath = targetDir.resolve(sourceFile.getName());

            // 如果目标文件已存在，添加时间戳后缀
            if (Files.exists(targetFilePath)) {
                String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HHmmss"));
                String fileName = sourceFile.getName();
                String nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
                String extension = fileName.substring(fileName.lastIndexOf('.'));
                String newFileName = nameWithoutExt + "_" + timestamp + extension;
                targetFilePath = targetDir.resolve(newFileName);
            }

            // 移动文件
            Files.move(sourceFile.toPath(), targetFilePath, StandardCopyOption.REPLACE_EXISTING);

            String absolutePath = targetFilePath.toAbsolutePath().toString();
            logger.info("文件移动成功: {} -> {}", sourceFile.getAbsolutePath(), absolutePath);

            return absolutePath;

        } catch (IOException e) {
            logger.error("移动文件失败: {}", sourceFile.getAbsolutePath(), e);
            return null;
        }
    }

    /**
     * 获取jar文件所在目录
     */
    private String getJarDirectory() {
        try {
            // 获取当前类的位置
            String jarPath = DataCollectionService.class.getProtectionDomain()
                    .getCodeSource().getLocation().toURI().getPath();

            File jarFile = new File(jarPath);
            if (jarFile.isFile()) {
                // 如果是jar文件，返回其父目录
                return jarFile.getParent();
            } else {
                // 如果是目录（开发环境），返回项目根目录
                return System.getProperty("user.dir");
            }
        } catch (Exception e) {
            logger.warn("获取jar目录失败，使用当前工作目录", e);
            return System.getProperty("user.dir");
        }
    }

    /**
     * 解析成功后更新数据库记录
     */
    private boolean updateAfterParsingSuccess(Long detectionDataId, String collectDataJson, String collectPath) {
        try {
            // 更新解析状态、表单数据和文件路径
            boolean updateSuccess = deviceDetectionDataService.updateAfterParsingWithCollectData(
                detectionDataId,
                1, // 解析成功状态
                "解析成功",
                collectDataJson,
                collectPath,
                "system"
            );

            if (updateSuccess) {
                logger.info("成功更新解析成功状态和文件路径，记录ID: {}, 新路径: {}", detectionDataId, collectPath);
                return true;
            } else {
                logger.error("更新解析成功状态失败，记录ID: {}", detectionDataId);
                return false;
            }

        } catch (Exception e) {
            logger.error("更新解析成功状态异常，记录ID: {}", detectionDataId, e);
            return false;
        }
    }

    /**
     * 数据采集结果类
     */
    public static class DataCollectionResult {
        private final boolean success;
        private final String errorMessage;
        private final int processedCount;

        private DataCollectionResult(boolean success, String errorMessage, int processedCount) {
            this.success = success;
            this.errorMessage = errorMessage;
            this.processedCount = processedCount;
        }

        public static DataCollectionResult success() {
            return new DataCollectionResult(true, null, 0);
        }

        public static DataCollectionResult success(int processedCount) {
            return new DataCollectionResult(true, null, processedCount);
        }

        public static DataCollectionResult failure(String errorMessage) {
            return new DataCollectionResult(false, errorMessage, 0);
        }

        public boolean isSuccess() {
            return success;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public int getProcessedCount() {
            return processedCount;
        }
    }
}
