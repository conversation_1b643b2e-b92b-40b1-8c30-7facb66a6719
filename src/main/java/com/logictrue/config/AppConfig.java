package com.logictrue.config;

import com.logictrue.model.ExternalApp;
import com.logictrue.model.FormField;

import java.util.ArrayList;
import java.util.List;

/**
 * 应用配置数据类
 */
public class AppConfig {

    private String deviceId = "";
    private String formName = "数据采集表单";
    private String heartbeatUrl = "http://localhost:8080/api/heartbeat";
    private int heartbeatInterval = 30; // 心跳间隔，单位：秒
    private String imageUrl = "http://localhost:8080/api/device/image";
    private String backgroundImagePath = "";
    private String collectionPath = "";

    // 模板下载相关配置
    private String templateDownloadUrl = "http://localhost:8080/templateDownload";
    private String templateStoragePath = "templates";
    private boolean autoDownloadTemplate = true;

    // Excel采集相关配置
    private String excelCollectionPath = "";
    private boolean autoExcelCollection = false;
    private int collectionIntervalMinutes = 30;

    // 数据推送相关配置
    private String pushUrl = "http://localhost:8080/api/push";
    private boolean autoPushEnabled = true;
    private int pushRetryTimes = 3;
    private int pushTimeoutSeconds = 30;

    // 主页标题相关配置
    private String mainPageTitle = "欢迎使用IoT数据采集系统";
    private double mainPageTitleFontSize = 24.0;
    private String mainPageTitleColor = "#333333";
    private double mainPageTitleTopMargin = 50.0;

    private List<FormField> formFields = new ArrayList<>();
    private List<ExternalApp> externalApps = new ArrayList<>();

    public AppConfig() {
        initializeDefaultFormFields();
    }

    /**
     * 初始化默认表单字段
     */
    private void initializeDefaultFormFields() {
        if (formFields.isEmpty()) {
            // 添加默认的表单字段
            FormField carCodeField = new FormField();
            carCodeField.setId("carCode");
            carCodeField.setLabel("车号");
            carCodeField.setName("carCode");
            carCodeField.setType(FormField.FieldType.TEXT);
            carCodeField.setRequired(true); // 设为必填
            formFields.add(carCodeField);

            FormField operatorField = new FormField();
            operatorField.setId("operator");
            operatorField.setLabel("检测人");
            operatorField.setName("operator");
            operatorField.setType(FormField.FieldType.TEXT);
            operatorField.setRequired(true); // 设为必填
            formFields.add(operatorField);

            FormField partNameField = new FormField();
            partNameField.setId("partName");
            partNameField.setLabel("部件名称");
            partNameField.setName("partName");
            partNameField.setType(FormField.FieldType.TEXT);
            partNameField.setRequired(false); // 非必填
            formFields.add(partNameField);

            FormField partCodeField = new FormField();
            partCodeField.setId("partCode");
            partCodeField.setLabel("部件编码");
            partCodeField.setName("partCode");
            partCodeField.setType(FormField.FieldType.TEXT);
            partCodeField.setRequired(false); // 非必填
            formFields.add(partCodeField);

            FormField checkDateField = new FormField();
            checkDateField.setId("checkDate");
            checkDateField.setLabel("检测日期");
            checkDateField.setName("checkDate");
            checkDateField.setType(FormField.FieldType.DATE);
            checkDateField.setRequired(true); // 设为必填
            formFields.add(checkDateField);

            FormField remarkField = new FormField();
            remarkField.setId("remark");
            remarkField.setLabel("备注");
            remarkField.setName("remark");
            remarkField.setType(FormField.FieldType.TEXTAREA);
            remarkField.setRequired(false); // 非必填
            formFields.add(remarkField);
        }
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getFormName() {
        return formName;
    }

    public void setFormName(String formName) {
        this.formName = formName;
    }



    public String getHeartbeatUrl() {
        return heartbeatUrl;
    }

    public void setHeartbeatUrl(String heartbeatUrl) {
        this.heartbeatUrl = heartbeatUrl;
    }

    public int getHeartbeatInterval() {
        return heartbeatInterval;
    }

    public void setHeartbeatInterval(int heartbeatInterval) {
        this.heartbeatInterval = heartbeatInterval;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getBackgroundImagePath() {
        return backgroundImagePath;
    }

    public void setBackgroundImagePath(String backgroundImagePath) {
        this.backgroundImagePath = backgroundImagePath;
    }

    public String getCollectionPath() {
        return collectionPath;
    }

    public void setCollectionPath(String collectionPath) {
        this.collectionPath = collectionPath;
    }

    public List<FormField> getFormFields() {
        if (formFields == null) {
            formFields = new ArrayList<>();
        }
        if (formFields.isEmpty()) {
            initializeDefaultFormFields();
        }
        return formFields;
    }

    public void setFormFields(List<FormField> formFields) {
        this.formFields = formFields;
    }

    public List<ExternalApp> getExternalApps() {
        if (externalApps == null) {
            externalApps = new ArrayList<>();
        }
        return externalApps;
    }

    public void setExternalApps(List<ExternalApp> externalApps) {
        this.externalApps = externalApps;
    }

    // 模板下载相关配置的getter和setter
    public String getTemplateDownloadUrl() {
        return templateDownloadUrl;
    }

    public void setTemplateDownloadUrl(String templateDownloadUrl) {
        this.templateDownloadUrl = templateDownloadUrl;
    }

    public String getTemplateStoragePath() {
        return templateStoragePath;
    }

    public void setTemplateStoragePath(String templateStoragePath) {
        this.templateStoragePath = templateStoragePath;
    }

    public boolean isAutoDownloadTemplate() {
        return autoDownloadTemplate;
    }

    public void setAutoDownloadTemplate(boolean autoDownloadTemplate) {
        this.autoDownloadTemplate = autoDownloadTemplate;
    }

    // Excel采集相关配置的getter和setter
    public String getExcelCollectionPath() {
        return excelCollectionPath;
    }

    public void setExcelCollectionPath(String excelCollectionPath) {
        this.excelCollectionPath = excelCollectionPath;
    }

    public boolean isAutoExcelCollection() {
        return autoExcelCollection;
    }

    public void setAutoExcelCollection(boolean autoExcelCollection) {
        this.autoExcelCollection = autoExcelCollection;
    }

    public int getCollectionIntervalMinutes() {
        return collectionIntervalMinutes;
    }

    public void setCollectionIntervalMinutes(int collectionIntervalMinutes) {
        this.collectionIntervalMinutes = collectionIntervalMinutes;
    }

    // 数据推送相关配置的getter和setter
    public String getPushUrl() {
        return pushUrl;
    }

    public void setPushUrl(String pushUrl) {
        this.pushUrl = pushUrl;
    }

    public boolean isAutoPushEnabled() {
        return autoPushEnabled;
    }

    public void setAutoPushEnabled(boolean autoPushEnabled) {
        this.autoPushEnabled = autoPushEnabled;
    }

    public int getPushRetryTimes() {
        return pushRetryTimes;
    }

    public void setPushRetryTimes(int pushRetryTimes) {
        this.pushRetryTimes = pushRetryTimes;
    }

    public int getPushTimeoutSeconds() {
        return pushTimeoutSeconds;
    }

    public void setPushTimeoutSeconds(int pushTimeoutSeconds) {
        this.pushTimeoutSeconds = pushTimeoutSeconds;
    }

    // 主页标题相关配置的getter和setter
    public String getMainPageTitle() {
        return mainPageTitle;
    }

    public void setMainPageTitle(String mainPageTitle) {
        this.mainPageTitle = mainPageTitle;
    }

    public double getMainPageTitleFontSize() {
        return mainPageTitleFontSize;
    }

    public void setMainPageTitleFontSize(double mainPageTitleFontSize) {
        this.mainPageTitleFontSize = mainPageTitleFontSize;
    }

    public String getMainPageTitleColor() {
        return mainPageTitleColor;
    }

    public void setMainPageTitleColor(String mainPageTitleColor) {
        this.mainPageTitleColor = mainPageTitleColor;
    }

    public double getMainPageTitleTopMargin() {
        return mainPageTitleTopMargin;
    }

    public void setMainPageTitleTopMargin(double mainPageTitleTopMargin) {
        this.mainPageTitleTopMargin = mainPageTitleTopMargin;
    }

    @Override
    public String toString() {
        return "AppConfig{" +
                "deviceId='" + deviceId + '\'' +
                ", formName='" + formName + '\'' +
                ", heartbeatUrl='" + heartbeatUrl + '\'' +
                ", imageUrl='" + imageUrl + '\'' +
                ", backgroundImagePath='" + backgroundImagePath + '\'' +
                ", collectionPath='" + collectionPath + '\'' +
                ", templateDownloadUrl='" + templateDownloadUrl + '\'' +
                ", templateStoragePath='" + templateStoragePath + '\'' +
                ", autoDownloadTemplate=" + autoDownloadTemplate +
                ", excelCollectionPath='" + excelCollectionPath + '\'' +
                ", autoExcelCollection=" + autoExcelCollection +
                ", collectionIntervalMinutes=" + collectionIntervalMinutes +
                ", pushUrl='" + pushUrl + '\'' +
                ", autoPushEnabled=" + autoPushEnabled +
                ", pushRetryTimes=" + pushRetryTimes +
                ", pushTimeoutSeconds=" + pushTimeoutSeconds +
                '}';
    }
}
