package com.logictrue.config;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.logictrue.model.ExternalApp;
import com.logictrue.model.FormField;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 配置管理器，负责应用配置的持久化存储
 */
public class ConfigManager {
    private static final Logger logger = LoggerFactory.getLogger(ConfigManager.class);
    private static final String CONFIG_FILE = "config.json";
    private static final String CONFIG_DIR = getApplicationDirectory();

    private static ConfigManager instance;
    private AppConfig config;

    private ConfigManager() {
        loadConfig();
    }

    public static synchronized ConfigManager getInstance() {
        if (instance == null) {
            instance = new ConfigManager();
        }
        return instance;
    }

    /**
     * 获取应用程序配置目录
     */
    private static String getApplicationDirectory() {
        try {
            // 获取当前运行的jar文件路径
            String jarPath = ConfigManager.class.getProtectionDomain()
                    .getCodeSource()
                    .getLocation()
                    .toURI()
                    .getPath();

            File jarFile = new File(jarPath);

            // 检查是否在AppImage环境中运行
            if (isRunningInAppImage(jarPath)) {
                // AppImage环境：使用用户主目录下的应用配置目录
                String userHome = System.getProperty("user.home");
                String appConfigDir = userHome + File.separator + ".iotclient";
                logger.info("应用程序目录 (AppImage模式): {}", appConfigDir);
                return appConfigDir;
            }

            // 如果是jar文件，检查是否可写
            if (jarFile.isFile() && jarPath.endsWith(".jar")) {
                String appDir = jarFile.getParent();
                File testDir = new File(appDir);

                // 检查目录是否可写
                if (testDir.canWrite()) {
                    logger.info("应用程序目录 (jar模式): {}", appDir);
                    return appDir;
                } else {
                    // 如果jar所在目录不可写，使用用户主目录
                    String userHome = System.getProperty("user.home");
                    String appConfigDir = userHome + File.separator + ".iotclient";
                    logger.info("应用程序目录 (jar模式-只读，使用用户目录): {}", appConfigDir);
                    return appConfigDir;
                }
            }

            // 如果是在IDE中运行，返回项目根目录
            String userDir = System.getProperty("user.dir");
            logger.info("应用程序目录 (开发模式): {}", userDir);
            return userDir;

        } catch (URISyntaxException e) {
            logger.warn("无法获取应用程序目录，使用用户主目录", e);
            String userHome = System.getProperty("user.home");
            return userHome + File.separator + ".iotclient";
        }
    }

    /**
     * 检查是否在AppImage环境中运行
     */
    private static boolean isRunningInAppImage(String jarPath) {
        // AppImage会将应用挂载到/tmp/.mount_xxx目录
        return jarPath.startsWith("/tmp/.mount_") ||
               System.getenv("APPIMAGE") != null ||
               System.getenv("APPDIR") != null;
    }

    /**
     * 加载配置文件
     */
    private void loadConfig() {
        try {
            File configDir = new File(CONFIG_DIR);
            if (!configDir.exists()) {
                configDir.mkdirs();
            }

            File configFile = new File(configDir, CONFIG_FILE);
            if (configFile.exists()) {
                String configContent = Files.readString(configFile.toPath());
                config = JSON.parseObject(configContent, AppConfig.class);
                logger.info("配置文件加载成功");
            } else {
                config = new AppConfig();
                saveConfig();
                logger.info("创建默认配置文件");
            }
        } catch (IOException e) {
            logger.error("加载配置文件失败", e);
            config = new AppConfig();
        }
    }

    /**
     * 保存配置文件
     */
    public synchronized void saveConfig() {
        try {
            File configDir = new File(CONFIG_DIR);
            if (!configDir.exists()) {
                configDir.mkdirs();
            }

            File configFile = new File(configDir, CONFIG_FILE);
            File tempFile = new File(configDir, CONFIG_FILE + ".tmp");

            // 先写入临时文件，使用格式化的JSON
            String configJson = JSON.toJSONString(config, JSONWriter.Feature.PrettyFormat);
            Files.writeString(tempFile.toPath(), configJson);

            // 验证临时文件是否完整
            if (tempFile.exists() && tempFile.length() > 0) {
                // 备份原文件（如果存在）
                if (configFile.exists()) {
                    File backupFile = new File(configDir, CONFIG_FILE + ".bak");
                    Files.copy(configFile.toPath(), backupFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                }

                // 将临时文件重命名为正式文件
                Files.move(tempFile.toPath(), configFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                logger.info("配置文件保存成功: {}", configFile.getAbsolutePath());
            } else {
                logger.error("临时配置文件写入失败");
                if (tempFile.exists()) {
                    tempFile.delete();
                }
            }
        } catch (IOException e) {
            logger.error("保存配置文件失败", e);
        }
    }

    public AppConfig getConfig() {
        return config;
    }

    public void setConfig(AppConfig config) {
        this.config = config;
        saveConfig();
    }

    // 便捷方法
    public String getDeviceId() {
        return config.getDeviceId();
    }

    public void setDeviceId(String deviceId) {
        config.setDeviceId(deviceId);
        saveConfig();
    }

    public String getFormName() {
        return config.getFormName();
    }

    public void setFormName(String formName) {
        config.setFormName(formName);
        saveConfig();
    }



    public String getHeartbeatUrl() {
        return config.getHeartbeatUrl();
    }

    public void setHeartbeatUrl(String heartbeatUrl) {
        config.setHeartbeatUrl(heartbeatUrl);
        saveConfig();
    }

    public int getHeartbeatInterval() {
        return config.getHeartbeatInterval();
    }

    public void setHeartbeatInterval(int heartbeatInterval) {
        config.setHeartbeatInterval(heartbeatInterval);
        saveConfig();
    }

    public String getImageUrl() {
        return config.getImageUrl();
    }

    public void setImageUrl(String imageUrl) {
        config.setImageUrl(imageUrl);
        saveConfig();
    }

    public String getBackgroundImagePath() {
        return config.getBackgroundImagePath();
    }

    public void setBackgroundImagePath(String backgroundImagePath) {
        config.setBackgroundImagePath(backgroundImagePath);
        saveConfig();
    }

    public String getCollectionPath() {
        return config.getCollectionPath();
    }

    public void setCollectionPath(String collectionPath) {
        config.setCollectionPath(collectionPath);
        saveConfig();
    }

    public List<FormField> getFormFields() {
        List<FormField> fields = config.getFormFields();
        logger.info("ConfigManager获取表单字段，共{}个", fields.size());
        return fields;
    }

    public void setFormFields(List<FormField> formFields) {
        config.setFormFields(formFields);
        saveConfig();
    }

    public void addFormField(FormField formField) {
        config.getFormFields().add(formField);
        saveConfig();
    }

    public void removeFormField(String fieldId) {
        config.getFormFields().removeIf(field -> field.getId().equals(fieldId));
        saveConfig();
    }

    public void updateFormField(FormField updatedField) {
        List<FormField> fields = config.getFormFields();
        for (int i = 0; i < fields.size(); i++) {
            if (fields.get(i).getId().equals(updatedField.getId())) {
                fields.set(i, updatedField);
                break;
            }
        }
        saveConfig();
    }

    // 外部应用程序管理方法
    public List<ExternalApp> getExternalApps() {
        return config.getExternalApps();
    }

    public void setExternalApps(List<ExternalApp> externalApps) {
        config.setExternalApps(externalApps);
        saveConfig();
    }

    public void addExternalApp(ExternalApp app) {
        config.getExternalApps().add(app);
        saveConfig();
    }

    public void removeExternalApp(String appId) {
        config.getExternalApps().removeIf(app -> app.getId().equals(appId));
        saveConfig();
    }

    public void updateExternalApp(ExternalApp updatedApp) {
        List<ExternalApp> apps = config.getExternalApps();
        for (int i = 0; i < apps.size(); i++) {
            if (apps.get(i).getId().equals(updatedApp.getId())) {
                apps.set(i, updatedApp);
                break;
            }
        }
        saveConfig();
    }

    // 新增的模板下载相关配置方法
    public String getTemplateDownloadUrl() {
        return config.getTemplateDownloadUrl();
    }

    public void setTemplateDownloadUrl(String templateDownloadUrl) {
        config.setTemplateDownloadUrl(templateDownloadUrl);
        saveConfig();
    }

    public String getTemplateStoragePath() {
        return config.getTemplateStoragePath();
    }

    public void setTemplateStoragePath(String templateStoragePath) {
        config.setTemplateStoragePath(templateStoragePath);
        saveConfig();
    }

    public boolean isAutoDownloadTemplate() {
        return config.isAutoDownloadTemplate();
    }

    public void setAutoDownloadTemplate(boolean autoDownloadTemplate) {
        config.setAutoDownloadTemplate(autoDownloadTemplate);
        saveConfig();
    }

    // Excel采集相关配置方法
    public String getExcelCollectionPath() {
        return config.getExcelCollectionPath();
    }

    public void setExcelCollectionPath(String excelCollectionPath) {
        config.setExcelCollectionPath(excelCollectionPath);
        saveConfig();
    }

    public boolean isAutoExcelCollection() {
        return config.isAutoExcelCollection();
    }

    public void setAutoExcelCollection(boolean autoExcelCollection) {
        config.setAutoExcelCollection(autoExcelCollection);
        saveConfig();
    }

    public int getCollectionIntervalMinutes() {
        return config.getCollectionIntervalMinutes();
    }

    public void setCollectionIntervalMinutes(int collectionIntervalMinutes) {
        config.setCollectionIntervalMinutes(collectionIntervalMinutes);
        saveConfig();
    }

    // 主页标题相关配置方法
    public String getMainPageTitle() {
        return config.getMainPageTitle();
    }

    public void setMainPageTitle(String mainPageTitle) {
        config.setMainPageTitle(mainPageTitle);
        saveConfig();
    }

    public double getMainPageTitleFontSize() {
        return config.getMainPageTitleFontSize();
    }

    public void setMainPageTitleFontSize(double mainPageTitleFontSize) {
        config.setMainPageTitleFontSize(mainPageTitleFontSize);
        saveConfig();
    }

    public String getMainPageTitleColor() {
        return config.getMainPageTitleColor();
    }

    public void setMainPageTitleColor(String mainPageTitleColor) {
        config.setMainPageTitleColor(mainPageTitleColor);
        saveConfig();
    }

    public double getMainPageTitleTopMargin() {
        return config.getMainPageTitleTopMargin();
    }

    public void setMainPageTitleTopMargin(double mainPageTitleTopMargin) {
        config.setMainPageTitleTopMargin(mainPageTitleTopMargin);
        saveConfig();
    }

    // 数据推送相关配置方法
    public String getPushUrl() {
        return config.getPushUrl();
    }

    public void setPushUrl(String pushUrl) {
        config.setPushUrl(pushUrl);
        saveConfig();
    }

    public boolean isAutoPushEnabled() {
        return config.isAutoPushEnabled();
    }

    public void setAutoPushEnabled(boolean autoPushEnabled) {
        config.setAutoPushEnabled(autoPushEnabled);
        saveConfig();
    }

    public int getPushRetryTimes() {
        return config.getPushRetryTimes();
    }

    public void setPushRetryTimes(int pushRetryTimes) {
        config.setPushRetryTimes(pushRetryTimes);
        saveConfig();
    }

    public int getPushTimeoutSeconds() {
        return config.getPushTimeoutSeconds();
    }

    public void setPushTimeoutSeconds(int pushTimeoutSeconds) {
        config.setPushTimeoutSeconds(pushTimeoutSeconds);
        saveConfig();
    }

    // 配置文件备份和恢复功能
    /**
     * 备份配置文件
     */
    public boolean backupConfig(String backupPath) {
        try {
            File configFile = new File(CONFIG_DIR, CONFIG_FILE);
            if (!configFile.exists()) {
                logger.warn("配置文件不存在，无法备份");
                return false;
            }

            File backupFile = new File(backupPath);
            Files.copy(configFile.toPath(), backupFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            logger.info("配置文件备份成功: {}", backupPath);
            return true;
        } catch (IOException e) {
            logger.error("备份配置文件失败", e);
            return false;
        }
    }

    /**
     * 自动备份配置文件（带时间戳）
     */
    public String autoBackupConfig() {
        try {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String backupFileName = "config_backup_" + timestamp + ".json";
            String backupPath = CONFIG_DIR + File.separator + backupFileName;

            if (backupConfig(backupPath)) {
                return backupPath;
            }
        } catch (Exception e) {
            logger.error("自动备份配置文件失败", e);
        }
        return null;
    }

    /**
     * 从备份文件恢复配置
     */
    public boolean restoreConfig(String backupPath) {
        try {
            File backupFile = new File(backupPath);
            if (!backupFile.exists()) {
                logger.warn("备份文件不存在: {}", backupPath);
                return false;
            }

            // 验证备份文件格式
            String backupContent = Files.readString(backupFile.toPath());
            AppConfig testConfig = JSON.parseObject(backupContent, AppConfig.class);
            if (testConfig == null) {
                logger.warn("备份文件格式无效");
                return false;
            }

            // 备份当前配置
            autoBackupConfig();

            // 恢复配置
            File configFile = new File(CONFIG_DIR, CONFIG_FILE);
            Files.copy(backupFile.toPath(), configFile.toPath(), StandardCopyOption.REPLACE_EXISTING);

            // 重新加载配置
            loadConfig();

            logger.info("配置文件恢复成功: {}", backupPath);
            return true;
        } catch (Exception e) {
            logger.error("恢复配置文件失败", e);
            return false;
        }
    }

    /**
     * 导出配置文件到指定路径
     */
    public boolean exportConfig(String exportPath) {
        return backupConfig(exportPath);
    }

    /**
     * 从指定路径导入配置文件
     */
    public boolean importConfig(String importPath) {
        return restoreConfig(importPath);
    }

    /**
     * 获取配置目录路径
     */
    public String getConfigDir() {
        return CONFIG_DIR;
    }

    /**
     * 获取配置文件路径
     */
    public String getConfigFilePath() {
        return CONFIG_DIR + File.separator + CONFIG_FILE;
    }
}
